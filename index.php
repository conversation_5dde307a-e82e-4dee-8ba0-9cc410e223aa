<?php
/**
 * The main template file
 */

get_header(); ?>

<main class="main-content">
    <!-- Videos being watched section -->
    <section class="featured-videos">
        <div class="section-header">
            <h2 class="section-title">Videos being watched</h2>
            <a href="<?php echo esc_url(add_query_arg('filter', 'random')); ?>" class="more-link">More videos</a>
        </div>
        
        <div class="video-grid" id="featured-videos-grid">
            <?php
            // Query for featured/random videos
            $featured_args = array(
                'post_type' => 'video',
                'posts_per_page' => 12,
                'orderby' => 'rand',
                'post_status' => 'publish'
            );
            
            $featured_query = new WP_Query($featured_args);
            
            if ($featured_query->have_posts()) :
                while ($featured_query->have_posts()) : $featured_query->the_post();
                    get_template_part('template-parts/video-item');
                endwhile;
                wp_reset_postdata();
            else :
                echo '<p>No videos found. Please add some videos from the admin panel.</p>';
            endif;
            ?>
        </div>
    </section>

    <!-- Recently added videos section -->
    <section class="recent-videos">
        <div class="section-header">
            <h2 class="section-title">Recently added videos</h2>
        </div>
        
        <!-- Filter tabs -->
        <div class="filter-tabs">
            <button class="filter-tab active" data-filter="latest">Recently added videos</button>
            <button class="filter-tab" data-filter="most-viewed">Most viewed videos</button>
            <button class="filter-tab" data-filter="longest">Longest videos</button>
            <button class="filter-tab" data-filter="popular">Popular videos</button>
            <button class="filter-tab" data-filter="random">Random videos</button>
        </div>
        
        <div class="video-grid" id="filtered-videos-grid">
            <?php
            // Query for recent videos
            $recent_args = array(
                'post_type' => 'video',
                'posts_per_page' => 36,
                'orderby' => 'date',
                'order' => 'DESC',
                'post_status' => 'publish',
                'paged' => get_query_var('paged') ? get_query_var('paged') : 1
            );
            
            $recent_query = new WP_Query($recent_args);
            
            if ($recent_query->have_posts()) :
                while ($recent_query->have_posts()) : $recent_query->the_post();
                    get_template_part('template-parts/video-item');
                endwhile;
                wp_reset_postdata();
                
                // Pagination
                famoustube_pagination($recent_query);
            else :
                echo '<p>No recent videos found.</p>';
            endif;
            ?>
        </div>
        
        <div id="loading" style="display: none; text-align: center; padding: 2rem;">
            <p>Loading...</p>
        </div>
    </section>
</main>

<?php get_footer(); ?>
