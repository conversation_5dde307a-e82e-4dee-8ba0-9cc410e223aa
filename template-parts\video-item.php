<?php
/**
 * Template part for displaying video items
 */

$video_id = get_the_ID();
$duration = get_post_meta($video_id, '_video_duration', true);
$views = get_post_meta($video_id, '_video_views', true);
$rating = get_post_meta($video_id, '_video_rating', true);
$is_premium = get_post_meta($video_id, '_is_premium', true);
$video_url = get_post_meta($video_id, '_video_url', true);

// Default values if meta doesn't exist
if (empty($views)) $views = rand(1000, 10000);
if (empty($rating)) $rating = rand(75, 98);
if (empty($duration)) $duration = sprintf('%02d:%02d', rand(4, 8), rand(10, 59));
?>

<article class="video-item" data-video-id="<?php echo esc_attr($video_id); ?>">
    <div class="video-thumbnail">
        <a href="<?php the_permalink(); ?>">
            <?php if (has_post_thumbnail()) : ?>
                <?php the_post_thumbnail('video-thumbnail', array('alt' => get_the_title())); ?>
            <?php else : ?>
                <img src="<?php echo get_template_directory_uri(); ?>/img/default-thumbnail.jpg" alt="<?php the_title_attribute(); ?>" />
            <?php endif; ?>
            
            <?php if ($duration) : ?>
                <span class="video-duration"><?php echo famoustube_format_duration($duration); ?></span>
            <?php endif; ?>
            
            <?php if ($is_premium) : ?>
                <span class="premium-badge">Premium</span>
            <?php endif; ?>
        </a>
    </div>
    
    <div class="video-info">
        <h3 class="video-title">
            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
        </h3>
        
        <div class="video-meta">
            <span class="video-views"><?php echo famoustube_format_views($views); ?></span>
            <?php if ($rating) : ?>
                <span class="video-rating"><?php echo famoustube_get_rating($rating); ?></span>
            <?php endif; ?>
        </div>
    </div>
</article>
