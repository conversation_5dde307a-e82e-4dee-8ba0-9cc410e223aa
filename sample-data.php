<?php
/**
 * Sample Data Generator for FamousTube Theme
 * Run this file once to populate your site with demo content
 * Access via: yoursite.com/wp-content/themes/famoustube/sample-data.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('../../../wp-load.php');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to access this page.');
}

// Sample video data
$sample_videos = array(
    array(
        'title' => 'Amateur sex tape with sexy blonde girlfriend',
        'duration' => '06:10',
        'views' => 7441,
        'rating' => 91,
        'is_premium' => true,
        'categories' => array('Amateur', 'Blonde'),
        'tags' => array('sexy', 'girlfriend', 'tape'),
        'actors' => array('Blonde Beauty')
    ),
    array(
        'title' => '<PERSON><PERSON><PERSON> is a horny and slutty housewife',
        'duration' => '04:56',
        'views' => 8235,
        'rating' => 87,
        'is_premium' => false,
        'categories' => array('Asian', 'MILF'),
        'tags' => array('housewife', 'japanese', 'horny'),
        'actors' => array('Azusa Ono')
    ),
    array(
        'title' => 'Busty amateur wife fucking in the sauna',
        'duration' => '06:27',
        'views' => 6601,
        'rating' => 93,
        'is_premium' => false,
        'categories' => array('Amateur', 'MILF'),
        'tags' => array('busty', 'wife', 'sauna'),
        'actors' => array('Busty Wife')
    ),
    array(
        'title' => 'Bountiful Breasts – S4:E2',
        'duration' => '05:00',
        'views' => 8584,
        'rating' => 92,
        'is_premium' => false,
        'categories' => array('Big Tits', 'Series'),
        'tags' => array('breasts', 'series', 'episode'),
        'actors' => array('Series Star')
    ),
    array(
        'title' => 'Hot college slut fucking until strong orgasm',
        'duration' => '06:02',
        'views' => 8558,
        'rating' => 98,
        'is_premium' => true,
        'categories' => array('College', 'Teen'),
        'tags' => array('college', 'orgasm', 'young'),
        'actors' => array('College Girl')
    ),
    array(
        'title' => 'Natural Passions – S10:E5',
        'duration' => '05:02',
        'views' => 4762,
        'rating' => 93,
        'is_premium' => false,
        'categories' => array('Natural', 'Series'),
        'tags' => array('natural', 'passion', 'series'),
        'actors' => array('Natural Beauty')
    ),
    array(
        'title' => 'Chihiro Akino surprises a fan by going to his house and fucking him',
        'duration' => '04:53',
        'views' => 2909,
        'rating' => 89,
        'is_premium' => false,
        'categories' => array('Asian', 'Surprise'),
        'tags' => array('surprise', 'fan', 'house'),
        'actors' => array('Chihiro Akino')
    ),
    array(
        'title' => 'Real amateur sex from shameless couple',
        'duration' => '01:31',
        'views' => 7574,
        'rating' => 95,
        'is_premium' => false,
        'categories' => array('Amateur', 'Couple'),
        'tags' => array('real', 'amateur', 'couple'),
        'actors' => array('Amateur Couple')
    ),
    array(
        'title' => 'Big titted doll gets her asshole cum filled',
        'duration' => '06:12',
        'views' => 7427,
        'rating' => 79,
        'is_premium' => true,
        'categories' => array('Big Tits', 'Anal'),
        'tags' => array('big tits', 'anal', 'cumshot'),
        'actors' => array('Big Tit Doll')
    ),
    array(
        'title' => 'Stepmom Wont Leave Us Alone – S1:E3',
        'duration' => '08:00',
        'views' => 5165,
        'rating' => 86,
        'is_premium' => false,
        'categories' => array('MILF', 'Series'),
        'tags' => array('stepmom', 'series', 'taboo'),
        'actors' => array('Stepmom Star')
    )
);

// Function to create sample videos
function create_sample_videos($videos) {
    $created_count = 0;
    
    foreach ($videos as $video_data) {
        // Create the post
        $post_data = array(
            'post_title' => $video_data['title'],
            'post_content' => 'This is a sample video description. Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
            'post_status' => 'publish',
            'post_type' => 'video',
            'post_author' => 1
        );
        
        $post_id = wp_insert_post($post_data);
        
        if ($post_id) {
            // Add meta data
            update_post_meta($post_id, '_video_duration', $video_data['duration']);
            update_post_meta($post_id, '_video_views', $video_data['views']);
            update_post_meta($post_id, '_video_rating', $video_data['rating']);
            update_post_meta($post_id, '_is_premium', $video_data['is_premium'] ? 1 : 0);
            
            // Add categories
            if (!empty($video_data['categories'])) {
                $category_ids = array();
                foreach ($video_data['categories'] as $category_name) {
                    $term = wp_insert_term($category_name, 'video_category');
                    if (!is_wp_error($term)) {
                        $category_ids[] = $term['term_id'];
                    } else {
                        // Term already exists
                        $existing_term = get_term_by('name', $category_name, 'video_category');
                        if ($existing_term) {
                            $category_ids[] = $existing_term->term_id;
                        }
                    }
                }
                wp_set_post_terms($post_id, $category_ids, 'video_category');
            }
            
            // Add tags
            if (!empty($video_data['tags'])) {
                $tag_ids = array();
                foreach ($video_data['tags'] as $tag_name) {
                    $term = wp_insert_term($tag_name, 'video_tag');
                    if (!is_wp_error($term)) {
                        $tag_ids[] = $term['term_id'];
                    } else {
                        $existing_term = get_term_by('name', $tag_name, 'video_tag');
                        if ($existing_term) {
                            $tag_ids[] = $existing_term->term_id;
                        }
                    }
                }
                wp_set_post_terms($post_id, $tag_ids, 'video_tag');
            }
            
            // Add actors
            if (!empty($video_data['actors'])) {
                $actor_ids = array();
                foreach ($video_data['actors'] as $actor_name) {
                    $term = wp_insert_term($actor_name, 'actor');
                    if (!is_wp_error($term)) {
                        $actor_ids[] = $term['term_id'];
                    } else {
                        $existing_term = get_term_by('name', $actor_name, 'actor');
                        if ($existing_term) {
                            $actor_ids[] = $existing_term->term_id;
                        }
                    }
                }
                wp_set_post_terms($post_id, $actor_ids, 'actor');
            }
            
            $created_count++;
        }
    }
    
    return $created_count;
}

// Check if we should create sample data
if (isset($_GET['create_sample_data']) && $_GET['create_sample_data'] == '1') {
    $created = create_sample_videos($sample_videos);
    echo "<h2>Sample Data Created!</h2>";
    echo "<p>Created {$created} sample videos with categories, tags, and actors.</p>";
    echo "<p><a href='" . home_url() . "'>View your site</a></p>";
} else {
    echo "<h2>FamousTube Sample Data Generator</h2>";
    echo "<p>This will create sample video posts with meta data, categories, tags, and actors.</p>";
    echo "<p><strong>Warning:</strong> This will add content to your database. Only run this on a development site.</p>";
    echo "<p><a href='?create_sample_data=1' onclick='return confirm(\"Are you sure you want to create sample data?\")'>Create Sample Data</a></p>";
}
?>
