<?php
/**
 * The template for displaying single video posts
 */

get_header(); ?>

<main class="main-content">
    <?php while (have_posts()) : the_post(); ?>
        <article class="single-video">
            <header class="video-header">
                <h1 class="video-title"><?php the_title(); ?></h1>
                
                <div class="video-meta">
                    <?php
                    $views = get_post_meta(get_the_ID(), '_video_views', true);
                    $rating = get_post_meta(get_the_ID(), '_video_rating', true);
                    $duration = get_post_meta(get_the_ID(), '_video_duration', true);
                    $is_premium = get_post_meta(get_the_ID(), '_is_premium', true);
                    ?>
                    
                    <span class="video-views"><?php echo famoustube_format_views($views); ?></span>
                    <?php if ($rating) : ?>
                        <span class="video-rating"><?php echo famoustube_get_rating($rating); ?></span>
                    <?php endif; ?>
                    <?php if ($duration) : ?>
                        <span class="video-duration">Duration: <?php echo famoustube_format_duration($duration); ?></span>
                    <?php endif; ?>
                    <?php if ($is_premium) : ?>
                        <span class="premium-badge">Premium</span>
                    <?php endif; ?>
                </div>
            </header>

            <div class="video-player">
                <?php
                $video_url = get_post_meta(get_the_ID(), '_video_url', true);
                if ($video_url) :
                ?>
                    <video controls width="100%" height="400">
                        <source src="<?php echo esc_url($video_url); ?>" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                <?php else : ?>
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="video-placeholder">
                            <?php the_post_thumbnail('large'); ?>
                            <div class="play-button">▶</div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>

            <div class="video-content">
                <?php the_content(); ?>
            </div>

            <div class="video-taxonomies">
                <?php
                $categories = get_the_terms(get_the_ID(), 'video_category');
                if ($categories && !is_wp_error($categories)) :
                ?>
                    <div class="video-categories">
                        <strong>Categories:</strong>
                        <?php foreach ($categories as $category) : ?>
                            <a href="<?php echo get_term_link($category); ?>" class="category-link">
                                <?php echo esc_html($category->name); ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <?php
                $tags = get_the_terms(get_the_ID(), 'video_tag');
                if ($tags && !is_wp_error($tags)) :
                ?>
                    <div class="video-tags">
                        <strong>Tags:</strong>
                        <?php foreach ($tags as $tag) : ?>
                            <a href="<?php echo get_term_link($tag); ?>" class="tag-link">
                                <?php echo esc_html($tag->name); ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <?php
                $actors = get_the_terms(get_the_ID(), 'actor');
                if ($actors && !is_wp_error($actors)) :
                ?>
                    <div class="video-actors">
                        <strong>Actors:</strong>
                        <?php foreach ($actors as $actor) : ?>
                            <a href="<?php echo get_term_link($actor); ?>" class="actor-link">
                                <?php echo esc_html($actor->name); ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </article>

        <!-- Related Videos -->
        <section class="related-videos">
            <h2>Related Videos</h2>
            <div class="video-grid">
                <?php
                $related_args = array(
                    'post_type' => 'video',
                    'posts_per_page' => 8,
                    'post__not_in' => array(get_the_ID()),
                    'orderby' => 'rand'
                );

                // Try to get related videos by category
                $categories = get_the_terms(get_the_ID(), 'video_category');
                if ($categories && !is_wp_error($categories)) {
                    $category_ids = wp_list_pluck($categories, 'term_id');
                    $related_args['tax_query'] = array(
                        array(
                            'taxonomy' => 'video_category',
                            'field' => 'term_id',
                            'terms' => $category_ids,
                        ),
                    );
                }

                $related_query = new WP_Query($related_args);
                if ($related_query->have_posts()) :
                    while ($related_query->have_posts()) : $related_query->the_post();
                        get_template_part('template-parts/video-item');
                    endwhile;
                    wp_reset_postdata();
                endif;
                ?>
            </div>
        </section>
    <?php endwhile; ?>
</main>

<?php get_footer(); ?>
