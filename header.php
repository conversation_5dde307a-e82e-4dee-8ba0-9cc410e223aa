<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<header class="site-header">
    <div class="header-container">
        <div class="site-branding">
            <?php if (has_custom_logo()) : ?>
                <?php the_custom_logo(); ?>
            <?php else : ?>
                <a href="<?php echo esc_url(home_url('/')); ?>" class="site-logo">
                    <?php bloginfo('name'); ?>
                </a>
            <?php endif; ?>
        </div>

        <nav class="main-navigation">
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_id' => 'primary-menu',
                'fallback_cb' => 'famoustube_fallback_menu',
            ));
            ?>
        </nav>

        <div class="user-actions">
            <?php if (is_user_logged_in()) : ?>
                <a href="<?php echo wp_logout_url(home_url()); ?>" class="logout-link">Logout</a>
            <?php else : ?>
                <button id="login-btn" class="btn">Login</button>
            <?php endif; ?>
        </div>
    </div>
</header>

<!-- Login Modal -->
<div id="login-modal" class="modal">
    <div class="modal-content">
        <span class="close" id="login-close">&times;</span>
        <h3>Login to FamousTube</h3>
        <form id="login-form" method="post">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn">Login</button>
            </div>
            <div class="form-links">
                <a href="#" id="forgot-password-link">Lost Password?</a>
                <span> | </span>
                <a href="#" id="register-link">Sign up</a>
            </div>
        </form>
    </div>
</div>

<!-- Register Modal -->
<div id="register-modal" class="modal">
    <div class="modal-content">
        <span class="close" id="register-close">&times;</span>
        <h3>Register to FamousTube</h3>
        <form id="register-form" method="post">
            <div class="form-group">
                <label for="reg-username">Username</label>
                <input type="text" id="reg-username" name="reg-username" required>
            </div>
            <div class="form-group">
                <label for="reg-email">Email</label>
                <input type="email" id="reg-email" name="reg-email" required>
            </div>
            <div class="form-group">
                <label for="reg-password">Password</label>
                <input type="password" id="reg-password" name="reg-password" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn">Register</button>
            </div>
            <div class="form-links">
                <a href="#" id="login-link">Already have an account? Login</a>
            </div>
        </form>
    </div>
</div>

<!-- Reset Password Modal -->
<div id="reset-modal" class="modal">
    <div class="modal-content">
        <span class="close" id="reset-close">&times;</span>
        <h3>Reset Password</h3>
        <p>Enter the username or e-mail you used in your profile. A password reset link will be sent to you by email.</p>
        <form id="reset-form" method="post">
            <div class="form-group">
                <label for="reset-email">Username or E-mail</label>
                <input type="text" id="reset-email" name="reset-email" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn">Get new password</button>
            </div>
            <div class="form-links">
                <a href="#" id="back-to-login">Back to Login</a>
            </div>
        </form>
    </div>
</div>

<?php
// Fallback menu function
function famoustube_fallback_menu() {
    echo '<ul id="primary-menu">';
    echo '<li><a href="' . esc_url(home_url('/')) . '">Home</a></li>';
    echo '<li><a href="' . esc_url(home_url('/categories/')) . '">Categories</a></li>';
    echo '<li><a href="' . esc_url(home_url('/tags/')) . '">Tags</a></li>';
    echo '<li><a href="' . esc_url(home_url('/actors/')) . '">Actors</a></li>';
    echo '<li><a href="' . esc_url(home_url('/category/premium/')) . '">Premium</a></li>';
    echo '</ul>';
}
?>
