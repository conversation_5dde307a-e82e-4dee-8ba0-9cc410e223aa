# FamousTube WordPress Theme

A modern video-focused WordPress theme inspired by the FamousTube design. This theme is perfect for video sharing websites, adult content sites, or any video-centric platform.

## Features

- **Custom Video Post Type** with meta fields for duration, views, rating, and premium status
- **Video Grid Layout** with hover effects and responsive design
- **Premium Content System** with membership restrictions
- **Filter Tabs** for sorting videos (Latest, Most Viewed, Longest, Popular, Random)
- **Custom Taxonomies** for Categories, Tags, and Actors
- **User Authentication** with login/register modals
- **Dark Theme** with modern styling
- **Responsive Design** for mobile and tablet devices
- **AJAX Functionality** for seamless filtering and interactions
- **SEO Friendly** with proper HTML5 structure

## Installation

1. **Download the theme** files to your WordPress themes directory:
   ```
   /wp-content/themes/famoustube/
   ```

2. **Activate the theme** in your WordPress admin panel:
   - Go to Appearance > Themes
   - Find "FamousTube" and click "Activate"

3. **Set up navigation menu**:
   - Go to Appearance > Menus
   - Create a new menu and assign it to "Primary Menu" location
   - Add pages for Categories, Tags, Actors, and Premium

4. **Configure permalinks**:
   - Go to Settings > Permalinks
   - Choose "Post name" or "Custom Structure" for SEO-friendly URLs

## Setup and Configuration

### 1. Create Sample Content

To quickly populate your site with demo content:

1. Visit: `yoursite.com/wp-content/themes/famoustube/sample-data.php`
2. Click "Create Sample Data" to add sample videos, categories, tags, and actors
3. **Note**: Only run this on development sites

### 2. Add Video Content

To add videos manually:

1. Go to Videos > Add New Video in your WordPress admin
2. Fill in the video details:
   - **Title**: Video title
   - **Content**: Video description
   - **Featured Image**: Video thumbnail
   - **Video Details**: Duration, views, rating, video URL, premium status
   - **Categories**: Assign video categories
   - **Tags**: Add relevant tags
   - **Actors**: Add actor names

### 3. Premium Content Setup

The theme includes a basic premium membership system:

- **Mark content as premium** in the video edit screen
- **Manage premium users** in Users > All Users (Premium Member column)
- **Grant premium access** by editing user profiles and checking "Premium Member"

### 4. Customize Appearance

- **Logo**: Go to Appearance > Customize > Site Identity
- **Colors**: Modify the CSS variables in `style.css`
- **Layout**: Adjust grid settings and responsive breakpoints

## File Structure

```
famoustube/
├── style.css                 # Main stylesheet
├── functions.php             # Theme functions and features
├── index.php                 # Main template file
├── header.php                # Header template
├── footer.php                # Footer template
├── single-video.php          # Single video template
├── taxonomy-video_category.php # Video category archive
├── taxonomy-video_tag.php    # Video tag archive
├── taxonomy-actor.php        # Actor archive
├── template-parts/
│   └── video-item.php        # Video grid item template
├── js/
│   └── script.js             # JavaScript functionality
├── img/                      # Images directory
├── sample-data.php           # Sample data generator
└── README.md                 # This file
```

## Customization

### Adding New Video Filters

To add new filter options, modify the `famoustube_filter_videos()` function in `functions.php`:

```php
case 'your-filter':
    $args['meta_key'] = '_your_meta_key';
    $args['orderby'] = 'meta_value_num';
    $args['order'] = 'DESC';
    break;
```

### Styling Modifications

Key CSS classes for customization:

- `.video-grid` - Main video grid container
- `.video-item` - Individual video items
- `.premium-badge` - Premium content indicator
- `.filter-tabs` - Filter navigation tabs
- `.modal` - Login/register modals

### Adding Custom Fields

To add new video meta fields:

1. Modify the `famoustube_video_details_callback()` function
2. Update the `famoustube_save_video_details()` function
3. Add display logic in templates

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Optimization

The theme includes several performance features:

- **Lazy loading** for images
- **AJAX pagination** to reduce page loads
- **Optimized CSS** with minimal external dependencies
- **Efficient database queries** for video filtering

## Security Features

- **Nonce verification** for AJAX requests
- **Input sanitization** for all user inputs
- **Capability checks** for admin functions
- **Premium content protection**

## Troubleshooting

### Videos not displaying
- Check if the "video" post type is registered
- Verify permalink structure is set correctly
- Ensure sample data was created or videos were added manually

### Filter tabs not working
- Check if jQuery is loaded
- Verify AJAX URL is correct in browser console
- Ensure nonce verification is working

### Premium content not restricted
- Check if user roles are set up correctly
- Verify premium meta fields are saved
- Test with different user accounts

## Support and Development

This theme was created as a demonstration of WordPress theme development. For production use, consider:

- Adding proper payment integration for premium memberships
- Implementing video streaming capabilities
- Adding advanced search and filtering options
- Integrating with CDN for video delivery
- Adding user-generated content features

## License

This theme is provided as-is for educational and development purposes. Please ensure compliance with all applicable laws and regulations when using for adult content or commercial purposes.
