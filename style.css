/*
Theme Name: FamousTube
Description: A modern video-focused WordPress theme inspired by FamousTube design
Version: 1.0
Author: Your Name
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: #1a1a1a;
    color: #ffffff;
    line-height: 1.6;
}

a {
    color: #ffffff;
    text-decoration: none;
}

a:hover {
    color: #ff6b6b;
}

/* Header Styles */
.site-header {
    background-color: #2c2c2c;
    padding: 1rem 0;
    border-bottom: 1px solid #333;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.site-logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: #ff6b6b;
}

.main-navigation ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.main-navigation a {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.main-navigation a:hover {
    background-color: #ff6b6b;
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 20px;
}

/* Video Grid */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.video-item {
    background-color: #2c2c2c;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s;
}

.video-item:hover {
    transform: translateY(-5px);
}

.video-thumbnail {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.premium-badge {
    position: absolute;
    top: 8px;
    left: 8px;
    background-color: #ff6b6b;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: bold;
}

.video-info {
    padding: 1rem;
}

.video-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.video-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #ccc;
}

.video-views {
    color: #999;
}

.video-rating {
    color: #4CAF50;
    font-weight: bold;
}

/* Filter Tabs */
.filter-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid #333;
}

.filter-tab {
    padding: 0.8rem 1.5rem;
    background: none;
    border: none;
    color: #ccc;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
}

.filter-tab.active,
.filter-tab:hover {
    color: #ff6b6b;
    border-bottom-color: #ff6b6b;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: 1.5rem;
    color: #ffffff;
}

.more-link {
    color: #ff6b6b;
    font-size: 0.9rem;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin: 2rem 0;
}

.pagination a,
.pagination span {
    padding: 0.5rem 1rem;
    background-color: #2c2c2c;
    border-radius: 4px;
    color: #ffffff;
    text-decoration: none;
}

.pagination .current {
    background-color: #ff6b6b;
}

.pagination a:hover {
    background-color: #ff6b6b;
}

/* Footer */
.site-footer {
    background-color: #2c2c2c;
    padding: 2rem 0;
    border-top: 1px solid #333;
    text-align: center;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
}

.modal-content {
    background-color: #2c2c2c;
    margin: 10% auto;
    padding: 2rem;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #fff;
}

/* Form Styles */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #ccc;
}

.form-group input {
    width: 100%;
    padding: 0.8rem;
    background-color: #1a1a1a;
    border: 1px solid #333;
    border-radius: 4px;
    color: #fff;
}

.btn {
    background-color: #ff6b6b;
    color: white;
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #ff5252;
}

/* Premium Content Restriction */
.premium-content-restriction {
    text-align: center;
    padding: 3rem 2rem;
    background-color: #2c2c2c;
    border-radius: 8px;
    margin: 2rem 0;
}

.premium-lock {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.premium-content-restriction h3 {
    color: #ff6b6b;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.premium-content-restriction p {
    color: #ccc;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.premium-upgrade-btn {
    background-color: #ff6b6b;
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 4px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.premium-upgrade-btn:hover {
    background-color: #ff5252;
}

/* Archive Pages */
.archive-header {
    margin-bottom: 2rem;
    text-align: center;
}

.archive-title {
    font-size: 2rem;
    color: #ff6b6b;
    margin-bottom: 1rem;
}

.archive-description {
    color: #ccc;
    max-width: 600px;
    margin: 0 auto;
}

/* Single Video Page */
.single-video {
    max-width: 800px;
    margin: 0 auto;
}

.video-header {
    margin-bottom: 2rem;
}

.video-header .video-title {
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.video-header .video-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
}

.video-player {
    margin-bottom: 2rem;
    background-color: #000;
    border-radius: 8px;
    overflow: hidden;
}

.video-placeholder {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.play-button {
    position: absolute;
    font-size: 4rem;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: color 0.3s;
}

.play-button:hover {
    color: #ff6b6b;
}

.video-content {
    margin-bottom: 2rem;
    line-height: 1.6;
}

.video-taxonomies {
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: #2c2c2c;
    border-radius: 8px;
}

.video-taxonomies > div {
    margin-bottom: 0.5rem;
}

.video-taxonomies a {
    display: inline-block;
    padding: 0.2rem 0.5rem;
    margin: 0.2rem;
    background-color: #ff6b6b;
    color: white;
    border-radius: 4px;
    font-size: 0.8rem;
    text-decoration: none;
}

.video-taxonomies a:hover {
    background-color: #ff5252;
}

.related-videos {
    margin-top: 3rem;
}

.related-videos h2 {
    margin-bottom: 1.5rem;
    color: #ff6b6b;
}

/* Navigation Toggle for Mobile */
.nav-toggle {
    display: none;
    background: none;
    border: none;
    color: #fff;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-toggle {
        display: block;
        position: absolute;
        top: 1rem;
        right: 20px;
    }

    .main-navigation {
        display: none;
        width: 100%;
    }

    .main-navigation.active {
        display: block;
    }

    .main-navigation ul {
        flex-direction: column;
        gap: 0.5rem;
    }

    .video-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }

    .filter-tabs {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .video-header .video-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .video-grid {
        grid-template-columns: 1fr;
    }

    .main-content {
        padding: 1rem 10px;
    }

    .archive-title {
        font-size: 1.5rem;
    }

    .video-header .video-title {
        font-size: 1.4rem;
    }
}
