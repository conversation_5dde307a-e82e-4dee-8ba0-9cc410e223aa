jQuery(document).ready(function($) {
    // Modal functionality
    const loginModal = $('#login-modal');
    const registerModal = $('#register-modal');
    const resetModal = $('#reset-modal');
    const premiumModal = $('#premium-modal');
    
    // Open login modal
    $('#login-btn').on('click', function() {
        loginModal.show();
    });
    
    // Close modals
    $('.close').on('click', function() {
        $(this).closest('.modal').hide();
    });
    
    // Close modal when clicking outside
    $(window).on('click', function(e) {
        if ($(e.target).hasClass('modal')) {
            $(e.target).hide();
        }
    });
    
    // Switch between modals
    $('#register-link').on('click', function(e) {
        e.preventDefault();
        loginModal.hide();
        registerModal.show();
    });
    
    $('#login-link').on('click', function(e) {
        e.preventDefault();
        registerModal.hide();
        loginModal.show();
    });
    
    $('#forgot-password-link').on('click', function(e) {
        e.preventDefault();
        loginModal.hide();
        resetModal.show();
    });
    
    $('#back-to-login').on('click', function(e) {
        e.preventDefault();
        resetModal.hide();
        loginModal.show();
    });
    
    // Filter tabs functionality
    $('.filter-tab').on('click', function() {
        const filter = $(this).data('filter');
        const $this = $(this);
        
        // Update active tab
        $('.filter-tab').removeClass('active');
        $this.addClass('active');
        
        // Show loading
        $('#loading').show();
        $('#filtered-videos-grid').html('');
        
        // AJAX request to filter videos
        $.ajax({
            url: famoustube_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'filter_videos',
                filter: filter,
                paged: 1,
                nonce: famoustube_ajax.nonce
            },
            success: function(response) {
                $('#loading').hide();
                $('#filtered-videos-grid').html(response);
            },
            error: function() {
                $('#loading').hide();
                $('#filtered-videos-grid').html('<p>Error loading videos. Please try again.</p>');
            }
        });
    });
    
    // Video hover effects
    $(document).on('mouseenter', '.video-item', function() {
        $(this).find('.video-thumbnail img').css('transform', 'scale(1.05)');
    });
    
    $(document).on('mouseleave', '.video-item', function() {
        $(this).find('.video-thumbnail img').css('transform', 'scale(1)');
    });
    
    // Premium modal trigger (you can add this to premium video clicks)
    $(document).on('click', '.premium-badge', function(e) {
        e.preventDefault();
        premiumModal.show();
    });
    
    // Form submissions (basic validation)
    $('#login-form').on('submit', function(e) {
        e.preventDefault();
        const username = $('#username').val();
        const password = $('#password').val();
        
        if (!username || !password) {
            alert('Please fill in all fields.');
            return;
        }
        
        // Here you would typically send an AJAX request to handle login
        alert('Login functionality would be implemented here.');
    });
    
    $('#register-form').on('submit', function(e) {
        e.preventDefault();
        const username = $('#reg-username').val();
        const email = $('#reg-email').val();
        const password = $('#reg-password').val();
        
        if (!username || !email || !password) {
            alert('Please fill in all fields.');
            return;
        }
        
        // Here you would typically send an AJAX request to handle registration
        alert('Registration functionality would be implemented here.');
    });
    
    $('#reset-form').on('submit', function(e) {
        e.preventDefault();
        const email = $('#reset-email').val();
        
        if (!email) {
            alert('Please enter your username or email.');
            return;
        }
        
        // Here you would typically send an AJAX request to handle password reset
        alert('Password reset functionality would be implemented here.');
    });
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        const target = $($(this).attr('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });
    
    // Lazy loading for images (basic implementation)
    const lazyImages = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    lazyImages.forEach(img => imageObserver.observe(img));
    
    // View counter (simulate view increment on video click)
    $(document).on('click', '.video-item a', function() {
        const videoId = $(this).closest('.video-item').data('video-id');
        
        // AJAX call to increment view count
        $.ajax({
            url: famoustube_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'increment_video_views',
                video_id: videoId,
                nonce: famoustube_ajax.nonce
            }
        });
    });
    
    // Responsive navigation toggle (for mobile)
    const navToggle = $('<button class="nav-toggle">☰</button>');
    $('.header-container').prepend(navToggle);
    
    navToggle.on('click', function() {
        $('.main-navigation').toggleClass('active');
    });
    
    // Hide navigation when clicking outside on mobile
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.main-navigation, .nav-toggle').length) {
            $('.main-navigation').removeClass('active');
        }
    });
    
    // Search functionality (if search form exists)
    $('#search-form').on('submit', function(e) {
        e.preventDefault();
        const searchTerm = $('#search-input').val();

        if (searchTerm.length < 3) {
            alert('Please enter at least 3 characters to search.');
            return;
        }

        // Redirect to search results or perform AJAX search
        window.location.href = '/?s=' + encodeURIComponent(searchTerm);
    });

    // Premium membership AJAX
    $('.premium-btn').on('click', function(e) {
        e.preventDefault();

        if (!confirm('This is a demo. In a real implementation, this would redirect to a payment processor.')) {
            return;
        }

        $.ajax({
            url: famoustube_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'premium_signup',
                nonce: famoustube_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data);
                    location.reload();
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
            }
        });
    });

    // Smooth animations for video items
    $(document).on('mouseenter', '.video-item', function() {
        $(this).find('.video-thumbnail img').css({
            'transform': 'scale(1.05)',
            'transition': 'transform 0.3s ease'
        });
    });

    $(document).on('mouseleave', '.video-item', function() {
        $(this).find('.video-thumbnail img').css({
            'transform': 'scale(1)',
            'transition': 'transform 0.3s ease'
        });
    });

    // Keyboard navigation for modals
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            $('.modal:visible').hide();
        }
    });

    // Auto-hide loading indicator after timeout
    setTimeout(function() {
        $('#loading').hide();
    }, 10000);
});
