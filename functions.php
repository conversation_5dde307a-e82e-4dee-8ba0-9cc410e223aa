<?php
/**
 * FamousTube Theme Functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Theme setup
function famoustube_setup() {
    // Add theme support for various features
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));

    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'famoustube'),
    ));

    // Set post thumbnail size
    set_post_thumbnail_size(280, 200, true);
    add_image_size('video-thumbnail', 280, 200, true);
}
add_action('after_setup_theme', 'famoustube_setup');

// Enqueue scripts and styles
function famoustube_scripts() {
    wp_enqueue_style('famoustube-style', get_stylesheet_uri(), array(), '1.0.0');
    wp_enqueue_script('famoustube-script', get_template_directory_uri() . '/js/script.js', array('jquery'), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('famoustube-script', 'famoustube_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('famoustube_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'famoustube_scripts');

// Register custom post type for videos
function famoustube_register_video_post_type() {
    $labels = array(
        'name' => 'Videos',
        'singular_name' => 'Video',
        'menu_name' => 'Videos',
        'add_new' => 'Add New Video',
        'add_new_item' => 'Add New Video',
        'edit_item' => 'Edit Video',
        'new_item' => 'New Video',
        'view_item' => 'View Video',
        'search_items' => 'Search Videos',
        'not_found' => 'No videos found',
        'not_found_in_trash' => 'No videos found in trash'
    );

    $args = array(
        'labels' => $labels,
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-video-alt3',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'rewrite' => array('slug' => 'video'),
        'taxonomies' => array('video_category', 'video_tag', 'actor')
    );

    register_post_type('video', $args);
}
add_action('init', 'famoustube_register_video_post_type');

// Register custom taxonomies
function famoustube_register_taxonomies() {
    // Video Categories
    register_taxonomy('video_category', 'video', array(
        'labels' => array(
            'name' => 'Video Categories',
            'singular_name' => 'Video Category',
        ),
        'hierarchical' => true,
        'public' => true,
        'rewrite' => array('slug' => 'categories'),
    ));

    // Video Tags
    register_taxonomy('video_tag', 'video', array(
        'labels' => array(
            'name' => 'Video Tags',
            'singular_name' => 'Video Tag',
        ),
        'hierarchical' => false,
        'public' => true,
        'rewrite' => array('slug' => 'tags'),
    ));

    // Actors
    register_taxonomy('actor', 'video', array(
        'labels' => array(
            'name' => 'Actors',
            'singular_name' => 'Actor',
        ),
        'hierarchical' => false,
        'public' => true,
        'rewrite' => array('slug' => 'actors'),
    ));
}
add_action('init', 'famoustube_register_taxonomies');

// Add custom meta boxes for video data
function famoustube_add_video_meta_boxes() {
    add_meta_box(
        'video_details',
        'Video Details',
        'famoustube_video_details_callback',
        'video',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'famoustube_add_video_meta_boxes');

// Video details meta box callback
function famoustube_video_details_callback($post) {
    wp_nonce_field('famoustube_save_video_details', 'famoustube_video_details_nonce');
    
    $duration = get_post_meta($post->ID, '_video_duration', true);
    $views = get_post_meta($post->ID, '_video_views', true);
    $rating = get_post_meta($post->ID, '_video_rating', true);
    $is_premium = get_post_meta($post->ID, '_is_premium', true);
    $video_url = get_post_meta($post->ID, '_video_url', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="video_duration">Duration (mm:ss)</label></th>';
    echo '<td><input type="text" id="video_duration" name="video_duration" value="' . esc_attr($duration) . '" /></td></tr>';
    
    echo '<tr><th><label for="video_views">Views</label></th>';
    echo '<td><input type="number" id="video_views" name="video_views" value="' . esc_attr($views) . '" /></td></tr>';
    
    echo '<tr><th><label for="video_rating">Rating (%)</label></th>';
    echo '<td><input type="number" id="video_rating" name="video_rating" value="' . esc_attr($rating) . '" min="0" max="100" /></td></tr>';
    
    echo '<tr><th><label for="video_url">Video URL</label></th>';
    echo '<td><input type="url" id="video_url" name="video_url" value="' . esc_attr($video_url) . '" style="width: 100%;" /></td></tr>';
    
    echo '<tr><th><label for="is_premium">Premium Content</label></th>';
    echo '<td><input type="checkbox" id="is_premium" name="is_premium" value="1" ' . checked($is_premium, 1, false) . ' /></td></tr>';
    echo '</table>';
}

// Save video meta data
function famoustube_save_video_details($post_id) {
    if (!isset($_POST['famoustube_video_details_nonce']) || 
        !wp_verify_nonce($_POST['famoustube_video_details_nonce'], 'famoustube_save_video_details')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    $fields = array('video_duration', 'video_views', 'video_rating', 'video_url');
    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
        }
    }

    $is_premium = isset($_POST['is_premium']) ? 1 : 0;
    update_post_meta($post_id, '_is_premium', $is_premium);
}
add_action('save_post', 'famoustube_save_video_details');

// AJAX handler for video filtering
function famoustube_filter_videos() {
    check_ajax_referer('famoustube_nonce', 'nonce');
    
    $filter = sanitize_text_field($_POST['filter']);
    $paged = intval($_POST['paged']);
    
    $args = array(
        'post_type' => 'video',
        'posts_per_page' => 36,
        'paged' => $paged,
        'post_status' => 'publish'
    );
    
    switch ($filter) {
        case 'latest':
            $args['orderby'] = 'date';
            $args['order'] = 'DESC';
            break;
        case 'most-viewed':
            $args['meta_key'] = '_video_views';
            $args['orderby'] = 'meta_value_num';
            $args['order'] = 'DESC';
            break;
        case 'longest':
            $args['meta_key'] = '_video_duration';
            $args['orderby'] = 'meta_value';
            $args['order'] = 'DESC';
            break;
        case 'popular':
            $args['meta_key'] = '_video_rating';
            $args['orderby'] = 'meta_value_num';
            $args['order'] = 'DESC';
            break;
        case 'random':
            $args['orderby'] = 'rand';
            break;
    }
    
    $query = new WP_Query($args);
    
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            get_template_part('template-parts/video-item');
        }
    }
    
    wp_reset_postdata();
    wp_die();
}
add_action('wp_ajax_filter_videos', 'famoustube_filter_videos');
add_action('wp_ajax_nopriv_filter_videos', 'famoustube_filter_videos');

// Custom pagination function
function famoustube_pagination($query = null) {
    global $wp_query;
    if (!$query) $query = $wp_query;
    
    $big = 999999999;
    $paginate_links = paginate_links(array(
        'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
        'format' => '?paged=%#%',
        'current' => max(1, get_query_var('paged')),
        'total' => $query->max_num_pages,
        'prev_text' => '«',
        'next_text' => '»',
        'type' => 'array'
    ));
    
    if ($paginate_links) {
        echo '<div class="pagination">';
        foreach ($paginate_links as $link) {
            echo $link;
        }
        echo '</div>';
    }
}

// Helper function to format video duration
function famoustube_format_duration($duration) {
    if (empty($duration)) return '';
    return esc_html($duration);
}

// Helper function to format view count
function famoustube_format_views($views) {
    if (empty($views)) return '0 views';
    return number_format($views) . ' views';
}

// Helper function to get video rating
function famoustube_get_rating($rating) {
    if (empty($rating)) return '';
    return intval($rating) . '%';
}

// AJAX handler for incrementing video views
function famoustube_increment_video_views() {
    check_ajax_referer('famoustube_nonce', 'nonce');

    $video_id = intval($_POST['video_id']);
    if ($video_id) {
        $current_views = get_post_meta($video_id, '_video_views', true);
        $current_views = $current_views ? intval($current_views) : 0;
        update_post_meta($video_id, '_video_views', $current_views + 1);
    }

    wp_die();
}
add_action('wp_ajax_increment_video_views', 'famoustube_increment_video_views');
add_action('wp_ajax_nopriv_increment_video_views', 'famoustube_increment_video_views');

// Add search functionality for videos
function famoustube_search_videos($query) {
    if (!is_admin() && $query->is_main_query()) {
        if ($query->is_search()) {
            $query->set('post_type', array('post', 'video'));
        }
    }
}
add_action('pre_get_posts', 'famoustube_search_videos');

// Custom query for video archives
function famoustube_video_archive_query($query) {
    if (!is_admin() && $query->is_main_query()) {
        if (is_home() || is_front_page()) {
            $query->set('post_type', array('video'));
            $query->set('posts_per_page', 36);
        }
    }
}
add_action('pre_get_posts', 'famoustube_video_archive_query');

// Premium content functionality
function famoustube_is_premium_user() {
    if (!is_user_logged_in()) {
        return false;
    }

    $user_id = get_current_user_id();
    $is_premium = get_user_meta($user_id, 'is_premium_member', true);

    return $is_premium == '1';
}

// Check if content should be restricted
function famoustube_restrict_premium_content($content) {
    if (is_singular('video')) {
        $is_premium = get_post_meta(get_the_ID(), '_is_premium', true);

        if ($is_premium && !famoustube_is_premium_user()) {
            $premium_message = '
            <div class="premium-content-restriction">
                <div class="premium-lock">🔒</div>
                <h3>Premium Content</h3>
                <p>This is premium content. Please upgrade to a premium membership to access this video.</p>
                <button class="btn premium-upgrade-btn" onclick="document.getElementById(\'premium-modal\').style.display=\'block\'">Upgrade to Premium</button>
            </div>';

            return $premium_message;
        }
    }

    return $content;
}
add_filter('the_content', 'famoustube_restrict_premium_content');

// Add premium member role
function famoustube_add_premium_role() {
    add_role('premium_member', 'Premium Member', array(
        'read' => true,
        'access_premium_content' => true,
    ));
}
add_action('init', 'famoustube_add_premium_role');

// AJAX handler for premium membership signup (basic implementation)
function famoustube_premium_signup() {
    check_ajax_referer('famoustube_nonce', 'nonce');

    if (!is_user_logged_in()) {
        wp_send_json_error('Please login first.');
        return;
    }

    $user_id = get_current_user_id();

    // In a real implementation, you would integrate with a payment processor here
    // For demo purposes, we'll just mark the user as premium
    update_user_meta($user_id, 'is_premium_member', '1');

    wp_send_json_success('Premium membership activated!');
}
add_action('wp_ajax_premium_signup', 'famoustube_premium_signup');

// Add premium indicator to admin user list
function famoustube_add_premium_column($columns) {
    $columns['premium_member'] = 'Premium Member';
    return $columns;
}
add_filter('manage_users_columns', 'famoustube_add_premium_column');

function famoustube_show_premium_column($value, $column_name, $user_id) {
    if ($column_name == 'premium_member') {
        $is_premium = get_user_meta($user_id, 'is_premium_member', true);
        return $is_premium ? 'Yes' : 'No';
    }
    return $value;
}
add_action('manage_users_custom_column', 'famoustube_show_premium_column', 10, 3);

// Add premium meta box to user profile
function famoustube_add_premium_profile_field($user) {
    $is_premium = get_user_meta($user->ID, 'is_premium_member', true);
    ?>
    <h3>Premium Membership</h3>
    <table class="form-table">
        <tr>
            <th><label for="is_premium_member">Premium Member</label></th>
            <td>
                <input type="checkbox" name="is_premium_member" id="is_premium_member" value="1" <?php checked($is_premium, '1'); ?> />
                <span class="description">Check this box to grant premium membership.</span>
            </td>
        </tr>
    </table>
    <?php
}
add_action('show_user_profile', 'famoustube_add_premium_profile_field');
add_action('edit_user_profile', 'famoustube_add_premium_profile_field');

// Save premium profile field
function famoustube_save_premium_profile_field($user_id) {
    if (!current_user_can('edit_user', $user_id)) {
        return false;
    }

    $is_premium = isset($_POST['is_premium_member']) ? '1' : '0';
    update_user_meta($user_id, 'is_premium_member', $is_premium);
}
add_action('personal_options_update', 'famoustube_save_premium_profile_field');
add_action('edit_user_profile_update', 'famoustube_save_premium_profile_field');
?>
