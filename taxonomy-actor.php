<?php
/**
 * The template for displaying actor archives
 */

get_header(); ?>

<main class="main-content">
    <div class="archive-header">
        <h1 class="archive-title">
            <?php single_term_title('Actor: '); ?>
        </h1>
        <?php if (term_description()) : ?>
            <div class="archive-description">
                <?php echo term_description(); ?>
            </div>
        <?php endif; ?>
    </div>

    <?php if (have_posts()) : ?>
        <div class="video-grid">
            <?php while (have_posts()) : the_post(); ?>
                <?php get_template_part('template-parts/video-item'); ?>
            <?php endwhile; ?>
        </div>

        <?php famoustube_pagination(); ?>
    <?php else : ?>
        <p>No videos found for this actor.</p>
    <?php endif; ?>
</main>

<?php get_footer(); ?>
